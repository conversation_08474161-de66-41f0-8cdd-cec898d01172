services:
  mysql_db:
    image: mysql:latest
    container_name: bhavani_doors_mysql
    restart: always
    environment:
      - MYSQL_DATABASE=bhavani_doors_prod
      - MYSQL_USER_FILE=/run/secrets/DB_USER_1
      - MYSQL_PASSWORD_FILE=/run/secrets/MYSQL_USER_PASS_1
      - MYSQL_ROOT_PASSWORD_FILE=/run/secrets/MYSQL_ROOT_PASSWORD
      - TZ=Asia/Kolkata
    secrets:
      - DB_USER_1
      - MYSQL_USER_PASS_1
      - MYSQL_ROOT_PASSWORD
      - LOAD_SCRIPT
    volumes:
      - dbdata:/var/lib/mysql
      - ./sql/load_database.sql:/docker-entrypoint-initdb.d/load_database.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: ./python
    container_name: app
    restart: always
    volumes:
      - .:/code
    environment:
      - SECRET_KEY=${SECRET_KEY:-$(python -c 'import secrets; print(secrets.token_hex(50))')}
      - DEBUG=True
      - ALLOWED_HOSTS=admin.bhavanihomedecors.co.in
      - DATABASE=bhavani_doors_prod
      - USER_FILE=/run/secrets/DB_USER_1
      - PASSWORD_FILE=/run/secrets/MYSQL_USER_PASS_1
      - HOST=mysql_db
      - PORT=3306
    secrets:
      - DB_USER_1
      - MYSQL_USER_PASS_1
    command: sh -c "python manage.py clearsessions && gunicorn web_projects.wsgi:application --bind app:8003 --timeout 120"
    depends_on:
      mysql_db:
        condition: service_healthy


  napp:
    build: ./nginx
    container_name: napp
    restart: always
    volumes:
      - nginx_logs:/var/log/nginx
      - ./static:/static
    secrets:
      - CERTS
    environment:
      - TZ=Asia/Kolkata
    depends_on:
      - app


  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: cloudflared
    restart: always
    command: tunnel --no-autoupdate run
    environment:
      - TUNNEL_TOKEN=${TOKEN}
      - TZ=Asia/Kolkata
    depends_on:
      - napp

volumes:
  dbdata:
  static:
  nginx_logs:

secrets:
  DB_USER_1:
    file: ../../secrets/db_user.txt
  MYSQL_USER_PASS_1:
    file: ../../secrets/db_user_pass.txt
  MYSQL_ROOT_PASSWORD:
    file: ../../secrets/root_password.txt
  LOAD_SCRIPT:
    file: ../../secrets/bhavani_prod_backup.sql
  CERTS:
    file: ../../secrets/certs