from io import BytesIO
from django.contrib import admin
from django.utils.html import format_html
from django.http import HttpResponse, JsonResponse
from bhavani_doors import models
from .models import order,order_item, gst_order_item,stock_order_item,order_return,stock_return,customer_payment
from django.urls import reverse
from reportlab.platypus import SimpleDocTemplate
from reportlab.lib.pagesizes import landscape, A4
from .utils import generate_gst_bill_pdf,generate_estimate_pdf,generate_material_statement_pdf,generate_customer_statement_pdf,generate_stock_order_pdf,generate_customer_balance_list_pdf
from reportlab.platypus import PageBreak



class order_itemAdminInline(admin.TabularInline):
    model = order_item
    extra = 1
    autocomplete_fields = ['material', 'unit']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        form = formset.form
        form.base_fields['material'].widget.can_change_related = False
        form.base_fields['material'].widget.can_add_related = False
        form.base_fields['material'].widget.can_delete_related = False
        form.base_fields['material'].widget.can_view_related = False
        form.base_fields['unit'].widget.can_change_related = False
        form.base_fields['unit'].widget.can_add_related = False
        form.base_fields['unit'].widget.can_delete_related = False
        form.base_fields['unit'].widget.can_view_related = False
        return formset

@admin.register(models.material)
class MaterialAdmin(admin.ModelAdmin):
    list_display = ('name', 'design', 'available_quantity', 'sales_type', 'unit','unit_order_price','type', 'stock_unit_price', 'total_stock_value')
    list_per_page = 5
    autocomplete_fields = ['design', 'unit']
    search_fields = ('name','design__name','design__color')
    ordering = ('name','design')
    actions = ['generate_statement_pdf']
    list_filter = ('type',)

    def generate_statement_pdf(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=A4,
                                rightMargin=20, leftMargin=20,
                                topMargin=20, bottomMargin=20)

        elements = []
        item_no = 0
        for material in queryset:
            # Generate PDF content for each material
            item_no += 1
            stmt_material = material
            order_items = order_item.objects.filter(material=material).order_by('order__date')
            stock_items = stock_order_item.objects.filter(material=material).order_by('order__date')
            order_return_items = order_return.objects.filter(material=material).order_by('date')
            stock_return_items = stock_return.objects.filter(material=material).order_by('date')
            material_statement = generate_material_statement_pdf(item_no,stmt_material,order_items,stock_items,order_return_items,stock_return_items)
            elements.extend(material_statement)
        doc.build(elements)
        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="material_statement.pdf"'
        return response
    
@admin.register(models.design)
class DesignAdmin(admin.ModelAdmin):
    list_display = ('name', 'color')
    list_per_page = 5     
    list_filter = ('name', 'color')
    ordering = ('name',)
    search_fields = ('name','color')

@admin.register(models.stock_order_item)
class stock_order_itemAdmin(admin.ModelAdmin):
    list_display = ('order', 'item_no', 'material', 'unit',  'unit_price','quantity', 'total_price')
    list_per_page = 5     
    search_fields = ('order__order_id','order__supplier__name',  'material__name', 'material__design__name', 'material__design__color')
    autocomplete_fields = ['order','material']
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if 'order_id' in request.GET:
            return qs.filter(order_id=request.GET['order_id'])
        else:
            return qs.filter(order__status='Open')

@admin.register(models.stock_order)
class stock_orderAdmin(admin.ModelAdmin):
    list_display = ('order_id', 'supplier', 'date', 'status', 'total_amt', 'view_stock_order_items')
    list_per_page = 5     
    ordering = ('-status','-date', )
    search_fields = ('supplier__name', 'order_id','date')
    autocomplete_fields = ['supplier']
    actions = ['generate_order_pdf',]

    def generate_order_pdf(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=landscape(A4),
                                rightMargin=10, leftMargin=10,
                                topMargin=10, bottomMargin=20)

        elements = []
        for order in queryset:
            # Generate PDF content for each material
            stmt_order = order
            order_items = stock_order_item.objects.filter(order=order).order_by('material__name')
            order_statement = generate_stock_order_pdf(stmt_order,order_items)
            elements.extend(order_statement)
            elements.append(PageBreak())
        doc.build(elements)
        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="stock_order.pdf"'
        return response

    def view_stock_order_items(self, obj):
        url = reverse('admin:bhavani_doors_stock_order_item_changelist') + f'?order_id={obj.order_id}'
        return format_html('<a href="{}">View Stock Order Items ({})</a>', url, obj.total_items)

@admin.register(models.supplier)
class supplierAdmin(admin.ModelAdmin):
    list_display = ('name', 'opening_amount','total_order_amt', 'total_payment', 'total_stock_return_amt','remaining_amount')
    list_per_page = 5
    search_fields = ('name', 'phone')
    ordering = ('name', )
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.exclude(name='Self')

@admin.register(models.customer)
class customerAdmin(admin.ModelAdmin):
    list_display = ('name', 'opening_amount','total_order_amt','view_orders','total_profit', 'total_payment', 'total_order_return_amt','total_freight','total_discount','remaining_amount','type')
    list_per_page = 5
    search_fields = ('name', 'phone') 
    ordering = ('name', )
    list_filter = ('type__type',)
    

    def view_orders(self, obj):
        url = reverse('admin:bhavani_doors_order_changelist') + f'?customer__id__exact={obj.id}'
        return format_html('<a href="{}">View Orders ({})</a>', url, obj.total_orders)
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        for field_name in form.base_fields:
            form.base_fields[field_name].widget.can_change_related = False
            form.base_fields[field_name].widget.can_add_related = False
            form.base_fields[field_name].widget.can_delete_related = False
            form.base_fields[field_name].widget.can_view_related = False
        return form   
    actions = ['generate_statement_pdf', 'generate_balance_list_pdf']

    def generate_statement_pdf(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=A4,
                                rightMargin=10, leftMargin=10,
                                topMargin=10, bottomMargin=10)

        elements = []
        item_no = 0

        for customer in queryset:
            # Generate PDF content for each customer
            item_no += 1
            orders = order.objects.filter(customer=customer).order_by('date')
            order_return_items = order_return.objects.filter(customer=customer).order_by('date')
            customer_payments = customer_payment.objects.filter(customer=customer).order_by('date')
            customer_statement = generate_customer_statement_pdf(item_no,customer,orders,order_return_items,customer_payments)
            elements.extend(customer_statement)
        doc.build(elements)
        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="customer_statement.pdf"'
        return response 
    
    def generate_balance_list_pdf(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=A4,
                                rightMargin=1, leftMargin=1,
                                topMargin=10, bottomMargin=10)

        elements = []
        ordered_customers = models.customer.objects.all()
        report_statement=generate_customer_balance_list_pdf(ordered_customers)
        elements.extend(report_statement)
        doc.build(elements)
        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="balance_list.pdf"'
        return response

@admin.register(models.order)
class orderAdmin(admin.ModelAdmin):
    list_display = ('order_id', 'customer', 'date', 'status', 'total_amt', 'total_profit','freight', 'view_order_items')
    list_per_page = 5   
    list_filter = ('status',) 
    search_fields = ('customer__name', 'order_id','customer__phone','date')
    actions = ['generate_estimate_pdf',]
    autocomplete_fields = ['customer']
    ordering = ('-status','-date')

    def view_order_items(self, obj):
        url = reverse('admin:bhavani_doors_order_item_changelist') + f'?order_id={obj.order_id}'
        return format_html('<a href="{}">View Order Items ({})</a>', url, obj.total_items)     

    def generate_estimate_pdf(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=A4,
                                rightMargin=20, leftMargin=20,
                                topMargin=20, bottomMargin=20)

        elements = []

        for order in queryset:
            # Generate PDF content for each order
            order_pdf_content = generate_estimate_pdf(order, order_item.objects.filter(order_id=order.order_id))
            elements.extend(order_pdf_content)
            elements.append(PageBreak())  # Add a page break between orders
        doc.build(elements)

        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="order_estimate.pdf"'
        return response

@admin.register(models.order_item)
class order_itemAdmin(admin.ModelAdmin):
    list_display = ('order', 'item_no', 'material', 'unit', 'unit_price', 'quantity', 'total_price', 'total_profit')
    list_per_page = 5    
    search_fields = ('order__order_id','order__customer__name', 'material__name', 'material__design__name', 'material__design__color')
    autocomplete_fields = ['material', 'order']
    ordering = ('order__order_id', '-item_no')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if 'order_id' in request.GET:
            return qs.filter(order_id=request.GET['order_id'])
        else:
            return qs.filter(order__status='Open')
    class Media:
            js = ('admin/js/admin_order_item.js',) 

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        for field_name in form.base_fields:
            form.base_fields[field_name].widget.can_change_related = False
            form.base_fields[field_name].widget.can_add_related = False
            form.base_fields[field_name].widget.can_delete_related = False
            form.base_fields[field_name].widget.can_view_related = False
        return form               

@admin.register(models.unit)
class unitAdmin(admin.ModelAdmin):
    list_display = ('name', 'qnty')
    list_per_page = 5    
    search_fields = ('name',)
    ordering = ('name', )

@admin.register(models.customer_payment)
class customer_paymentAdmin(admin.ModelAdmin):
    list_display = ( 'customer','date', 'amount','trans_type', 'ref_no','discount')
    list_per_page = 5    
    autocomplete_fields = ['customer']
    search_fields = ['id','customer__name',]

@admin.register(models.supplier_payment)
class supplier_paymentAdmin(admin.ModelAdmin):
    list_display = ( 'supplier','date', 'amount','trans_type', 'ref_no')
    list_per_page = 5    
    search_fields = ('supplier__name','supplier__phone', 'date', 'amount')
    autocomplete_fields = ['supplier']

@admin.register(models.stock_return)
class stock_returnAdmin(admin.ModelAdmin):
    list_display = ('sno','supplier', 'date', 'material', 'unit', 'unit_price','quantity', 'total_price')
    list_per_page = 5    
    search_fields = ('supplier__name', 'material__name', 'material__design__name', 'material__design__color','date')
    autocomplete_fields = ['supplier','material']

@admin.register(models.order_return)
class order_returnAdmin(admin.ModelAdmin):
    list_display = ('sno','customer', 'date', 'material', 'unit', 'unit_price','quantity', 'total_price')
    list_per_page = 5    
    search_fields = ('customer__name', 'material__name', 'material__design__name', 'material__design__color','date')
    ordering = ('-sno',)
    autocomplete_fields = ['customer','material']

@admin.register(models.group_materials_item)
class group_materials_itemAdmin(admin.ModelAdmin):
    list_display = ('group', 'item', 'unit', 'qnty', 'self_total_quantity')
    list_per_page = 5    
    autocomplete_fields = ['group','item']
  
@admin.register(models.material_type)
class material_typeAdmin(admin.ModelAdmin):
    list_display = ('name','total_value','total_profit')
    list_per_page = 1    

@admin.register(models.gst_tax)
class gst_taxAdmin(admin.ModelAdmin):
    list_display = ('next_invoice_no', 'cgst', 'sgst', 'igst')
    list_per_page = 5 

@admin.register(models.gst_order)
class gst_orderAdmin(admin.ModelAdmin):
    list_display = ('invoice_no','customer', 'view_order_items', 'total_price',  'gst_no', 'vehicle_no', 'address')
    list_per_page = 5 
    actions = ['generate_gst_bill_pdf']

    def generate_gst_bill_pdf(self, request, queryset):
        pdf_content = BytesIO()
        doc = SimpleDocTemplate(pdf_content, pagesize=A4,
                                rightMargin=20, leftMargin=20,
                                topMargin=20, bottomMargin=20)

        elements = []

        for orders in queryset:
            # Generate PDF content for each order
            order_pdf_content = generate_gst_bill_pdf(orders, gst_order_item.objects.filter(order_id=orders.id))
            elements.extend(order_pdf_content)
            elements.append(PageBreak())  # Add a page break between orders
        doc.build(elements)

        pdf_content.seek(0)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="order_estimate.pdf"'
        return response


    def view_order_items(self, obj):
        url = reverse('admin:bhavani_doors_gst_order_item_changelist') + f'?order_id={obj.id}'
        return format_html('<a href="{}">View Order Items ({})</a>', url, obj.total_items)

@admin.register(models.gst_material)
class gst_materialAdmin(admin.ModelAdmin):
    list_display = ('name', 'hsncode')
    list_per_page = 5
    search_fields = ('name', 'hsncode')
    ordering = ('name',)

@admin.register(models.gst_order_item)
class gst_order_itemAdmin(admin.ModelAdmin):
    list_display = ('order', 'item_no', 'material', 'unit', 'unit_price', 'quantity', 'total_price')
    list_per_page = 5   

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if 'order_id' in request.GET:
            return qs.filter(order_id=request.GET['order_id'])
        else:
            return qs.filter(order__status='Open')

@admin.register(models.customer_type)
class customer_typeAdmin(admin.ModelAdmin):
    list_display = ('type',)
    list_per_page = 5