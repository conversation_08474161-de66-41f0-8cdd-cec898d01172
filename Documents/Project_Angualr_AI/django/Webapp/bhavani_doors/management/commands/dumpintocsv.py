from django.core.management.base import BaseCommand
import csv
from datetime import datetime
from bhavani_doors.models import (
    material, design, unit, supplier, stock_order, stock_order_item,
    supplier_payment, stock_return, stock_order_history, stock_order_item_history,
    customer_type, customer, order, customer_payment, order_item, order_return,
    group_materials_item, material_type, gst_material, gst_tax, gst_order,
    gst_order_item
)

class Command(BaseCommand):
    help = 'Dumps all database records into CSV files including properties'

    def handle(self, *args, **kwargs):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Dictionary mapping model classes to their CSV filenames
        models_to_dump = {
            material: 'materials',
            design: 'designs', 
            unit: 'units',
            supplier: 'suppliers',
            stock_order: 'stock_orders',
            stock_order_item: 'stock_order_items',
            supplier_payment: 'supplier_payments',
            stock_return: 'stock_returns',
            stock_order_history: 'stock_order_history',
            stock_order_item_history: 'stock_order_item_history',
            customer_type: 'customer_types',
            customer: 'customers',
            order: 'orders',
            customer_payment: 'customer_payments', 
            order_item: 'order_items',
            order_return: 'order_returns',
            group_materials_item: 'group_materials_items',
            material_type: 'material_types',
            gst_material: 'gst_materials',
            gst_tax: 'gst_tax',
            gst_order: 'gst_orders',
            gst_order_item: 'gst_order_items'
        }

        for model, filename in models_to_dump.items():
            output_file = f'{filename}_{timestamp}.csv'
            
            # Get all records for the current model
            records = model.objects.all()
            
            if not records.exists():
                self.stdout.write(f'No records found for {filename}')
                continue

            # Get field names from the model including properties
            fields = [field.name for field in model._meta.fields]
            properties = [name for name in dir(model) if isinstance(getattr(model, name, None), property)]
            all_fields = fields + properties

            with open(output_file, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write header including properties
                writer.writerow(all_fields)
                
                # Write data rows including property values
                for record in records:
                    row = []
                    for field in all_fields:
                        try:
                            value = getattr(record, field)
                            row.append(value() if callable(value) else value)
                        except Exception as e:
                            row.append(None)  # Handle any errors in property calculation
                    writer.writerow(row)

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully dumped {records.count()} records with properties to {output_file}'
                )
            )
