from datetime import datetime
from decimal import Decimal
import random
from django import forms
from django.conf import settings
from django.db import models
from django.db.models import Sum, F
from django.core.validators import MinValueValidator
from django.urls import reverse


class material(models.Model):
    name = models.CharField(max_length=30)
    sales_type = models.CharField(max_length=15,choices=(('single','Single'),('group','Group')),default='single')
    design = models.ForeignKey('design', on_delete=models.RESTRICT)
    type = models.ForeignKey('material_type', on_delete=models.RESTRICT,default=2)    
    unit = models.ForeignKey('unit', on_delete=models.RESTRICT)
    unit_order_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))]) 
    opening_stock_quantity = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    opening_stock_unit_price = models.DecimalField(decimal_places=2, max_digits=12, default=0)        
    hst_stock_total_order_amt = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_stock_total_order_qnty = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_stock_total_return_amt = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_stock_total_return_qnty = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_order_total_order_amt = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_order_total_order_qnty = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_order_total_return_amt = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_order_total_return_qnty = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_order_total_profit = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    last_updated = models.DateTimeField(auto_now=True,editable=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name','design'], name='unique_material')
        ]
        verbose_name_plural = "d.0 Materials"
        
    def __str__(self):
        type_indicator = 'S' if self.sales_type == 'single' else 'G'
        return f"{self.name} {self.design} ({self.available_quantity} {self.unit}) [{type_indicator}]"
    

    @property
    def calc_total_stock_quantity(self):
        total_quantity = self.stock_order_item_set.filter(
            order__status='Delivered'
        ).aggregate(
            total=Sum(F('quantity') * F('unit__qnty'))
        )['total'] or 0
        return round((total_quantity / self.unit.qnty) + self.hst_stock_total_order_qnty, 2)
    
    @property
    def calc_total_stock_value(self):
        total_value = self.stock_order_item_set.filter(
            order__status='Delivered'
        ).aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        return round(total_value + self.hst_stock_total_order_amt, 2)
      
    @property
    def calc_total_stock_return_quantity(self):
        total_quantity = self.stock_return_set.all().aggregate(
            total=Sum(F('quantity') * F('unit__qnty'))
        )['total'] or 0
        return round((total_quantity / self.unit.qnty) + self.hst_stock_total_return_qnty, 2)

    @property
    def calc_total_stock_return_value(self):
        total_value = self.stock_return_set.all().aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        return round(total_value + self.hst_stock_total_return_amt, 2)

    @property
    def calc_total_order_quantity(self):
        from django.db.models import Sum, F
        total_quantity = self.order_item_set.all().aggregate(
            total=Sum(F('quantity') * F('unit__qnty'))
        )['total'] or 0
        return round((total_quantity / self.unit.qnty) + self.hst_order_total_order_qnty, 2)
    
    @property
    def calc_total_order_value(self):
        from django.db.models import Sum, F
        total_value = self.order_item_set.all().aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        return round(total_value + self.hst_order_total_order_amt, 2)
    
    @property
    def calc_total_order_return_quantity(self):
        from django.db.models import Sum, F
        total_quantity = self.order_return_set.all().aggregate(
            total=Sum(F('quantity') * F('unit__qnty'))
        )['total'] or 0
        return round((total_quantity / self.unit.qnty) + self.hst_order_total_return_qnty, 2)
    
    @property
    def calc_total_order_return_value(self):
        from django.db.models import Sum, F
        total_value = self.order_return_set.all().aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        return round(total_value + self.hst_order_total_return_amt, 2)

    @property
    def available_quantity(self):
        total = (((self.opening_stock_quantity/self.unit.qnty) + self.calc_total_stock_quantity) - self.calc_total_stock_return_quantity) - (self.calc_total_order_quantity - self.calc_total_order_return_quantity)
        if self.sales_type == 'single':
            grp_self_total = self.single_materials_items.all()
            self_total = 0
            for items in grp_self_total:
                self_total += items.self_total_quantity    
            total = total - (self_total/self.unit.qnty)  
        return round(total, 2)   
    
    @property
    def total_stock_value(self):
        return round(self.available_quantity * self.stock_unit_price, 2)
    
    @property
    def stock_unit_price(self):
        total_amt = (self.opening_stock_unit_price * self.opening_stock_quantity) + self.calc_total_stock_value - self.calc_total_stock_return_value
        total_qnty = self.calc_total_stock_quantity - self.calc_total_stock_return_quantity
        if total_qnty == 0:
            return 0
        else:
            return round(total_amt/total_qnty, 2)
        
    @property
    def total_profit(self):
        total = self.hst_order_total_profit
        for order_item in self.order_item_set.all():
            total += order_item.total_profit
        return round(total, 2)
            

class design(models.Model):
    name = models.CharField(max_length=15)
    color = models.CharField(max_length=10, null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name', 'color'], name='unique_design')
        ]
        verbose_name_plural = "c.0 Designs"

    def __str__(self):
        nm=''
        clr=''
        if self.name != "NA":
            nm = self.name
        if self.color != "NA":
            clr = self.color
        return f"{nm} {clr}"    
    
class unit(models.Model):
    name = models.CharField(max_length=15)
    qnty = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name', 'qnty'], name='unique_unit')
        ]
        verbose_name_plural = "c.1 Units"

    def __str__(self):
        return f"{self.name}"

class supplier(models.Model):
    name = models.CharField(max_length=30)
    date = models.DateTimeField(auto_now=True)
    phone = models.CharField(max_length=10)
    address = models.TextField()
    opening_amt = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    hst_total_order_amt = models.DecimalField(decimal_places=2,  max_digits=56, default=0,editable=False)
    hst_transport_charges = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)
    hst_unloading_charges = models.DecimalField(decimal_places=2, max_digits=56, default=0,editable=False)  
    hst_total_return_amt = models.DecimalField(decimal_places=2,  max_digits=56, default=0,editable=False)
    hst_total_payment = models.DecimalField(decimal_places=2,     max_digits=56, default=0,editable=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name','phone'], name='unique_supplier')
        ]
        verbose_name_plural = "a.0 Suppliers"

    @property
    def total_payment(self):
        from django.db.models import Sum
        total = supplier_payment.objects.filter(supplier=self).aggregate(
            total=Sum('amount')
        )['total'] or 0
        return round(self.hst_total_payment + total, 2)
    
    @property
    def total_stock_return_amt(self):
        from django.db.models import Sum, F
        total = stock_return.objects.filter(supplier=self).aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        return round(self.hst_total_return_amt + total, 2)
    
    @property
    def total_order_amt(self):
        from django.db.models import Sum
        total = stock_order.objects.filter(supplier=self).aggregate(
            total=Sum('transport_charges') + Sum('unloading_charges')
        )['total'] or 0
        
        # Get item-level totals
        from django.db.models import OuterRef, Subquery, Sum
        item_totals = stock_order_item.objects.filter(
            order__supplier=self
        ).values('order').annotate(
            order_total=Sum(F('quantity') * F('unit_price'))
        ).aggregate(
            total=Sum('order_total')
        )['total'] or 0
        
        return round(self.hst_total_order_amt + total + item_totals, 2)
    
    @property
    def remaining_amount(self):
        amt = (self.opening_amt + self.total_payment + self.total_stock_return_amt) - self.total_order_amt
        if amt == 0:
            return 0
        else:
            if amt > 0:
                return f"{round(amt, 2)} (Paid Extra)"
            else:
                return f"{round(amt * -1, 2)} (Debt)"

    @property
    def opening_amount(self):
        if self.opening_amt == 0:
            return 0
        else:
            if self.opening_amt > 0:
                return f"{round(self.opening_amt, 2)} (Paid Extra)"
            else:
                return f"{round(self.opening_amt * -1, 2)} (Debt)"


    def __str__(self):
        return self.name    
   
class stock_order(models.Model):
    order_id = models.CharField(primary_key=True, default='', editable=False, max_length=25)
    entry_type = models.CharField(max_length=20, choices=(('new', 'New'), ('change', 'Change'), ('delete', 'Delete'),('revert', 'Revert')), default='new')
    supplier = models.ForeignKey(supplier, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    status_list = (('open', 'Open'), ('delivered', 'Delivered'))
    status = models.CharField(max_length=20, choices=status_list, default=status_list[0][0])
    transport_charges = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    unloading_charges = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order_id','entry_type'], name='unique_stock_order')
        ]
        verbose_name_plural = "A.1 Stock Orders"

    def save(self, *args, **kwargs):
        if self.order_id == '':
            while True:
                random_order_id = random.randint(10000,99999)
                if not stock_order.objects.filter(order_id=random_order_id).exists():
                    self.order_id = f"{self.date.date()}-{random_order_id}"
                    break
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return f"https://{settings.ALLOWED_HOSTS[0]}{reverse('admin:bhavani_doors_stock_order_changelist') + f'?order_id__exact={self.order_id}'}"
        
    @property
    def total_amt(self):
        total = 0
        for stock_order_item in self.stock_order_item_set.all():
            total += stock_order_item.total_price
        return total

    @property
    def total_items(self):
        return self.stock_order_item_set.count()

    def __str__(self):
        return f"{self.supplier} {self.order_id}" 

class stock_order_history(models.Model):
    order_id = models.CharField(default='', editable=False, max_length=25)
    entry_type = models.CharField(max_length=20, choices=(('new', 'New'), ('change', 'Change'), ('delete', 'Delete'),('revert', 'Revert')), default='new')
    supplier = models.ForeignKey(supplier, on_delete=models.RESTRICT)
    stock_date = models.DateTimeField(default=datetime.now)
    status = models.CharField(max_length=20, choices=(('open', 'Open'), ('delivered', 'Delivered')), default='open')
    transport_charges = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    unloading_charges = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    notes = models.TextField(blank=True, null=True)
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order_id','entry_type'], name='unique_stock_order_history')
        ]
        verbose_name_plural = "A.1 Stock Orders History"

class supplier_payment(models.Model):
    supplier = models.ForeignKey(supplier, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    amount = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    TRANS_TYPE = (('cash', 'Cash'), ('online', 'Online'))
    trans_type = models.CharField(max_length=20, choices=TRANS_TYPE, default=TRANS_TYPE[0][0])
    ref_no = models.CharField(max_length=20, default='', blank=True, null=True)    

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['amount', 'date'], name='unique_supplier_payment')
        ] 
        verbose_name_plural = "A.4 Supplier Payments"

    def clean(self):
        try:
            if self.supplier.name == 'Self':
                raise forms.ValidationError("Self supplier can't make payment")
        except:
            pass
    def __str__(self):
        return f"{self.date} {self.amount}"
    
class stock_order_item(models.Model):
    order = models.ForeignKey(stock_order, on_delete=models.RESTRICT, 
                                 limit_choices_to={'status': 'open'})
    item_no = models.SmallIntegerField(default=0,editable=False)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order','material', 'quantity',  'unit_price'], name='unique_stock_order_item')
        ]
        verbose_name_plural = "A.2 Stock Order Items"

    def clean(self):
        try:
            if self.order.supplier.name == 'Self' and self.material.sales_type == 'single':
                raise forms.ValidationError("Self supplier can't order single material")
        except:
            pass
        try:
            if self.material.sales_type == 'group' and self.order.supplier.name == 'Self':
              group_materials_item_list = group_materials_item.objects.filter(group=self.material)
              for items in group_materials_item_list:
                    itms_qnty = items.qnty * items.unit.qnty * (self.quantity * self.unit.qnty)
                    tot_qnty_item = material.objects.filter(name=items.item.name, design=items.item.design)
                    tot_qnty = tot_qnty_item[0].available_quantity * tot_qnty_item[0].unit.qnty

                    if itms_qnty > tot_qnty:
                        raise forms.ValidationError(f"There is no enough quantity of {items.item.name} {items.item.design} in stock. Total quantity in stock is {round(tot_qnty/tot_qnty_item[0].unit.qnty,2)} {tot_qnty_item[0].unit.name}")
        except:
            pass

    def save(self, *args, **kwargs):
        if self.item_no == 0:
            last_item_no = stock_order_item.objects.filter(order_id=self.order_id).last().item_no if stock_order_item.objects.filter(order_id=self.order_id).exists() else 0
            self.item_no = last_item_no + 1
        super().save(*args, **kwargs)

    @property
    def total_price(self):
        return round(self.quantity * self.unit_price, 2)
    
    @property
    def stock_unit_nos_price(self):
        return round(self.unit_price / self.unit.qnty, 2)

    def __str__(self):
        return f"{self.order_id}"

class stock_order_item_history(models.Model):
    order = models.ForeignKey(stock_order_history, on_delete=models.RESTRICT)
    entry_type = models.CharField(max_length=20, choices=(('new', 'New'), ('change', 'Change'), ('delete', 'Delete'),('revert', 'Revert')), default='new')
    item_no = models.SmallIntegerField(default=0,editable=False)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order_id','entry_type','item_no'], name='unique_stock_order_item_history')
        ]
        verbose_name_plural = "A.2 Stock Order Items History"

class stock_return(models.Model):
    sno = models.PositiveIntegerField(primary_key=True, editable=False,default=0)
    supplier = models.ForeignKey(supplier, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])    
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['supplier','date','material', 'unit' ,'quantity',  'unit_price'], name='unique_stock_order_return')
        ]
        verbose_name_plural = "A.3 Stock Return"

    @property
    def total_price(self):
        return round(self.quantity * self.unit_price, 2)

    def __str__(self):
        return f"{self.supplier} {self.date} {self.material} {self.unit} {self.quantity}"
    
    def save(self, *args, **kwargs):
        if self.sno == 0:
            last_sno = stock_return.objects.last().sno if stock_return.objects.exists() else 0
            self.sno = last_sno + 1
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        return f"https://{settings.ALLOWED_HOSTS[0]}{reverse('admin:bhavani_doors_stock_return_changelist') + f'?sno__exact={self.sno}'}"        
    
class customer_type(models.Model):
    type = models.CharField(max_length=30)

    def __str__(self):
        return F"{self.type}"
    
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['type'], name='unique_customer_type')
        ]
        verbose_name_plural = "B.5 Customer Types"

class customer(models.Model):
    name = models.CharField(max_length=30)
    date = models.DateTimeField(auto_now=True)
    phone = models.CharField(max_length=10)
    address = models.TextField()
    opening_amt = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    type = models.ForeignKey(customer_type, on_delete=models.RESTRICT)
    hst_total_payment = models.DecimalField(decimal_places=2, max_digits=56, default=0, editable=False)
    hst_total_profit = models.DecimalField(decimal_places=2, max_digits=56, default=0, editable=False)
    hst_total_order_amt = models.DecimalField(decimal_places=2, max_digits=56, default=0, editable=False)
    hst_total_order_return_amt = models.DecimalField(decimal_places=2, max_digits=56, default=0, editable=False)
    hst_total_discount = models.DecimalField(decimal_places=2, max_digits=56, default=0, editable=False)
    hst_total_freight = models.DecimalField(decimal_places=2, max_digits=56, default=0, editable=False)

    def __str__(self):
        return F"{self.name}"
    
    @property
    def total_payment(self):
        from django.db.models import Sum
        total = customer_payment.objects.filter(customer=self).aggregate(
            total=Sum('amount')
        )['total'] or 0
        return round(self.hst_total_payment + total, 2)
    
    @property
    def total_order_count(self):
        return order.objects.filter(customer=self).count()

    @property
    def total_order_amt(self):
        from django.db.models import Sum
        total = order.objects.filter(customer=self).aggregate(
            total=Sum('freight')
        )['total'] or 0
        
        # Get item-level totals
        from django.db.models import F, Sum
        item_totals = order_item.objects.filter(
            order__customer=self
        ).aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        
        return round(self.hst_total_order_amt + total + item_totals, 2)
    
    @property
    def total_orders(self):
        return order.objects.filter(customer=self).count()

    @property
    def total_order_return_amt(self):
        from django.db.models import Sum, F
        total = order_return.objects.filter(customer=self).aggregate(
            total=Sum(F('quantity') * F('unit_price'))
        )['total'] or 0
        return round(self.hst_total_order_return_amt + total, 2)
    
    @property
    def total_discount(self):
        from django.db.models import Sum
        total = customer_payment.objects.filter(customer=self).aggregate(
            total=Sum('discount')
        )['total'] or 0
        return round(self.hst_total_discount + total, 2)
    
    @property
    def total_freight(self):
        from django.db.models import Sum
        total = order.objects.filter(customer=self).aggregate(
            total=Sum('freight')
        )['total'] or 0
        return round(self.hst_total_freight + total, 2)


    @property
    def remaining_amount(self):
        amt = (self.opening_amt + self.total_payment + self.total_order_return_amt + self.total_discount ) - (self.total_order_amt + self.total_freight)
        if amt == 0:
            return "0"
        else:
            if amt > 0:
                return f"{round(amt, 2)} (Paid Extra)"
            else:
                return f"{round(amt * -1, 2)} (Debt)"
            
    @property
    def remaining_amount_init(self):
        amt = (self.opening_amt + self.total_payment + self.total_order_return_amt + self.total_discount ) - self.total_order_amt
        return round(amt, 2)
    
    @property
    def opening_amount(self):
        if self.opening_amt == 0:
            return 0
        else:
            if self.opening_amt > 0:
                return f"{round(self.opening_amt, 2)} (Paid Extra)"
            else:
                return f"{round(self.opening_amt* -1, 2)} (Debt)"

    @property
    def total_profit(self):
        total = self.hst_total_profit
        for orders in order.objects.filter(customer=self):
            total += orders.total_profit
        return round(total, 2)


    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name','phone'], name='unique_customer')
        ]
        verbose_name_plural = "b.0 Customers"
        
class order(models.Model):
    order_id = models.CharField(primary_key=True, default='', editable=False, max_length=25)
    customer = models.ForeignKey(customer, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    status_list = (('open', 'Open'), ('delivered', 'Delivered'))
    status = models.CharField(max_length=20, choices=status_list, default=status_list[0][0])
    freight = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    entry_type = models.CharField(max_length=20, choices=(('new','New'), ('change','Change'), ('delete','Delete'), ('revert','Revert')), default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)

    class Meta:
        verbose_name_plural = "B.1 Orders"

    def save(self, *args, **kwargs):
        if self.order_id == '' or self.order_id is None:
            while True:
                random_order_id = random.randint(10000,99999)
                if not order.objects.filter(order_id=random_order_id).exists():
                    self.order_id = f"{self.date.date()}-{random_order_id}"
                    break
        super().save(*args, **kwargs)

    @property
    def total_amt(self):
        total = 0
        for order_item in self.order_item_set.all():
            total += order_item.total_price
        return total

    @property
    def total_items(self):
        return self.order_item_set.count()

    @property
    def total_orders(self):
        return order.objects.filter(order_id=self.order_id).count()
    
    @property
    def total_profit(self):
        return round(sum(order_itm.total_profit for order_itm in self.order_item_set.all()), 2)

    def __str__(self):
        return f"{self.customer} {self.order_id}"
    
    def get_absolute_url(self):
        return f"https://{settings.ALLOWED_HOSTS[0]}{reverse('admin:bhavani_doors_order_changelist') + f'?order_id__exact={self.order_id}'}"

class customer_payment(models.Model):
    customer = models.ForeignKey(customer, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    amount = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    discount = models.DecimalField(decimal_places=2, max_digits=8, default=0)
    TRANS_TYPE = (('cash', 'Cash'), ('online', 'Online'))
    trans_type = models.CharField(max_length=20, choices=TRANS_TYPE, default=TRANS_TYPE[0][0])
    ref_no = models.CharField(max_length=20, default='', blank=True, null=True)
    
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['amount', 'date'], name='unique_customer_payment')
        ]
        verbose_name_plural = "B.4 Customer Payments"

    def __str__(self):
        return f"{self.date}, {self.amount}"
    
class order_item(models.Model):
    order = models.ForeignKey(order, on_delete=models.RESTRICT, limit_choices_to={'status': 'open'})
    item_no = models.SmallIntegerField(default=0,editable=False)
    material = models.ForeignKey(material, on_delete=models.RESTRICT, related_name='order_item_set')
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order','material', 'quantity', 'unit_price'], name='unique_order_item')
        ]
        verbose_name_plural = "B.2 Order Items"

    def clean(self):
        try:      
            totalqnty = self.material.available_quantity * self.material.unit.qnty
            if self.quantity * self.unit.qnty > totalqnty:
                raise forms.ValidationError(f"Quantity cannot be greater than total quantity {totalqnty/self.material.unit.qnty} {self.material.unit.name}")
        except forms.ValidationError as e:
            raise forms.ValidationError(str(e))
        except:
            pass    

    def save(self, *args, **kwargs):
        if self.item_no == 0:
            last_item_no = order_item.objects.filter(order_id=self.order_id).last().item_no if order_item.objects.filter(order_id=self.order_id).exists() else 0
            self.item_no = last_item_no + 1
        super().save(*args, **kwargs)

    @property
    def total_price(self):
        return round(self.quantity * self.unit_price, 2)
    
    @property
    def total_profit(self):
        return round(self.total_price - (self.quantity * ((self.material.stock_unit_price/self.material.unit.qnty) * self.unit.qnty)), 2)

    @property
    def gst_total_price(self):
        return round(self.quantity * self.gst_unit_price, 2)   
   
    def __str__(self):
        return f"{self.order_id}"

class order_return(models.Model):
    sno = models.PositiveIntegerField(primary_key=True, editable=False,default=0)
    customer = models.ForeignKey(customer, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])    
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    entry_type = models.CharField(max_length=20, default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['customer','date','material', 'unit', 'quantity',  'unit_price'], name='unique_order_return')
         ]
        verbose_name_plural = "B.3 Order Returns"

    @property
    def total_price(self):
        return round(self.quantity * self.unit_price, 2)

    def __str__(self):
        return f"{self.customer} {self.date} {self.material} {self.unit} {self.quantity}"
    
    def save(self, *args, **kwargs):
        if self.sno == 0:
            last_sno = order_return.objects.last().sno if order_return.objects.exists() else 0
            self.sno = last_sno + 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return f"https://{settings.ALLOWED_HOSTS[0]}{reverse('admin:bhavani_doors_order_return_changelist') + f'?sno__exact={self.sno}'}"



class group_materials_item(models.Model):
    group = models.ForeignKey(material, on_delete=models.RESTRICT, limit_choices_to={'sales_type':'group'}, related_name='grp_materials_items')
    item = models.ForeignKey(material, on_delete=models.RESTRICT, limit_choices_to={'sales_type':'single'}, related_name='single_materials_items')
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    qnty = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['group','item'], name='unique_group_materials_item')
        ]
        verbose_name_plural = "d.2 Group Material Items"

    def __str__(self):
        return f"{self.group} {self.item}"  

    @property
    def self_total_quantity(self):
        from django.db.models import Sum, F
        # Calculate total quantity using aggregation
        total_qty = stock_order_item.objects.filter(
            material=self.group,
            order__supplier__name='Self'
        ).aggregate(
            total=Sum(F('quantity') * F('unit__qnty'))
        )['total'] or 0
        return round(total_qty * self.qnty * self.unit.qnty, 2)

class material_type(models.Model):
    name = models.CharField(max_length=30)
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name'], name='unique_material_type')
        ]
        verbose_name_plural = "A. Current Stock Value"

    @property
    def total_value(self):
        total_value = 0
        mtr = material.objects.filter(type=self)
        for item in mtr:
            total_value += item.total_stock_value
        return round(total_value, 2)

    @property
    def total_profit(self):
        total_profit = 0
        mtr = material.objects.filter(type=self)
        for item in mtr:
            total_profit += item.total_profit
        return round(total_profit, 2) 


    def __str__(self):
        return self.name

class gst_material(models.Model):
    name = models.CharField(max_length=30)
    hsncode = models.CharField(max_length=10)  

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name'], name='unique_gst_material')
        ]
        verbose_name_plural = "E.0 Materials"
    def __str__(self):
        return self.name

class gst_tax(models.Model):
    next_invoice_no = models.PositiveIntegerField(default=0)
    cgst = models.DecimalField(decimal_places=2, max_digits=4, validators=[MinValueValidator(Decimal('0.01'))])
    sgst = models.DecimalField(decimal_places=2, max_digits=4, validators=[MinValueValidator(Decimal('0.01'))])
    igst = models.DecimalField(decimal_places=2, max_digits=4, default=0)

    def __str__(self):
        return f"GST - {self.next_invoice_no}"
    
    class Meta:
        verbose_name_plural = "E.3 GST Tax"

class gst_order(models.Model):
    invoice_no = models.PositiveSmallIntegerField(default=0)
    date = models.DateTimeField(default=datetime.now)
    status_list = (('open', 'Open'), ('delivered', 'Delivered'))
    status = models.CharField(max_length=20, choices=status_list, default=status_list[0][0])
    customer = models.CharField(max_length=30)
    gst_no = models.CharField(max_length=15, blank=True, null=True)
    vehicle_no = models.CharField(max_length=15, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    cgst = models.DecimalField(decimal_places=2, max_digits=4, default=0,editable=False)
    sgst = models.DecimalField(decimal_places=2, max_digits=4, default=0,editable=False)
    igst = models.DecimalField(decimal_places=2, max_digits=4, default=0,editable=False)

    def __str__(self):
        return F"{self.customer} - {self.invoice_no}"

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['invoice_no'], name='unique_gst_order')
        ]
        verbose_name_plural = "E.1 Orders"

    def save(self, *args, **kwargs):        
        if self.invoice_no == 0:
            gst_item = gst_tax.objects.first()
            self.invoice_no = gst_item.next_invoice_no   
            self.cgst = gst_item.cgst
            self.sgst = gst_item.sgst
            self.igst = gst_item.igst   
            super().save(*args, **kwargs)
            gst_item.next_invoice_no = gst_item.next_invoice_no + 1
            gst_item.save()
        else:
            if self.cgst == 0 or self.sgst == 0 or self.igst == 0:
                gst_item = gst_tax.objects.first()
                self.cgst = gst_item.cgst
                self.sgst = gst_item.sgst
                self.igst = gst_item.igst
            super().save(*args, **kwargs)
    @property
    def total_price(self):
        total = 0
        for order_item in self.gst_order_item_set.all():
            total += order_item.total_price
        return total
    
    @property
    def total_items(self):
        return self.gst_order_item_set.count()

class gst_order_item(models.Model):
    order = models.ForeignKey(gst_order, on_delete=models.RESTRICT,limit_choices_to={'status': 'open'})
    item_no = models.SmallIntegerField(default=0,editable=False)
    material = models.ForeignKey(gst_material, on_delete=models.RESTRICT, related_name='gst_order_item')
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])

    class Meta: 
        constraints = [
            models.UniqueConstraint(fields=['order','material', 'quantity', 'unit_price'], name='unique_gst_order_item')
        ]
        verbose_name_plural = "E.2 Order Items"     

    def __str__(self):
        return f"{self.order.customer} - {self.order.invoice_no}"

    def save(self, *args, **kwargs):
        if self.item_no == 0:
            self.item_no = gst_order_item.objects.filter(order=self.order).count() + 1
        super().save(*args, **kwargs)   

    @property
    def total_price(self):  
        return round(self.quantity * self.unit_price, 2)
        
# --- HISTORY MODELS FOR ARCHIVAL ---

class order_history(models.Model):
    order_id = models.CharField(default='', editable=False, max_length=25)
    customer = models.ForeignKey(customer, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    status = models.CharField(max_length=20, choices=(('open', 'Open'), ('delivered', 'Delivered')), default='open')
    freight = models.DecimalField(decimal_places=2, max_digits=12, default=0)
    entry_type = models.CharField(max_length=20, choices=(('new','New'), ('change','Change'), ('delete','Delete'), ('revert','Revert')), default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order_id','entry_type'], name='unique_order_history')
        ]
        verbose_name_plural = "B.1 Orders History"

    def __str__(self):
        return f"{self.customer} {self.order_id}"

class order_item_history(models.Model):
    order = models.ForeignKey(order_history, on_delete=models.RESTRICT)
    entry_type = models.CharField(max_length=20, choices=(('new','New'), ('change','Change'), ('delete','Delete'), ('revert','Revert')), default='new')
    item_no = models.SmallIntegerField(default=0,editable=False)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    unit_price = models.DecimalField(decimal_places=2, max_digits=12, validators=[MinValueValidator(Decimal('0.01'))])
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['order','entry_type','item_no'], name='unique_order_item_history')
        ]
        verbose_name_plural = "B.2 Order Items History"

    def __str__(self):
        return f"{self.order.order_id} - Item {self.item_no}"

class customer_payment_history(models.Model):
    customer = models.ForeignKey(customer, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    amount = models.DecimalField(decimal_places=2, max_digits=12)
    discount = models.DecimalField(decimal_places=2, max_digits=8, default=0)
    trans_type = models.CharField(max_length=20)
    ref_no = models.CharField(max_length=20, default='', blank=True, null=True)
    entry_type = models.CharField(max_length=20, default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['customer','date','amount','entry_type'], name='unique_customer_payment_history')
        ]
        verbose_name_plural = "B.3 Customer Payments History"

class supplier_payment_history(models.Model):
    supplier = models.ForeignKey(supplier, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    amount = models.DecimalField(decimal_places=2, max_digits=12)
    trans_type = models.CharField(max_length=20)
    ref_no = models.CharField(max_length=20, default='', blank=True, null=True)
    entry_type = models.CharField(max_length=20, default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['supplier','date','amount','entry_type'], name='unique_supplier_payment_history')
        ]
        verbose_name_plural = "A.4 Supplier Payments History"

class stock_return_history(models.Model):
    supplier = models.ForeignKey(supplier, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12)
    unit_price = models.DecimalField(decimal_places=2, max_digits=12)
    entry_type = models.CharField(max_length=20, default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['supplier','date','material','unit','quantity','unit_price','entry_type'], name='unique_stock_return_history')
        ]
        verbose_name_plural = "A.3 Stock Return History"

class order_return_history(models.Model):
    customer = models.ForeignKey(customer, on_delete=models.RESTRICT)
    date = models.DateTimeField(default=datetime.now)
    material = models.ForeignKey(material, on_delete=models.RESTRICT)
    unit = models.ForeignKey(unit, on_delete=models.RESTRICT)
    quantity = models.DecimalField(decimal_places=2, max_digits=12)
    unit_price = models.DecimalField(decimal_places=2, max_digits=12)
    entry_type = models.CharField(max_length=20, default='new')
    version = models.PositiveIntegerField(default=0)
    parent_id = models.CharField(max_length=25, blank=True, null=True)
    archival_date = models.DateTimeField(default=datetime.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['customer','date','material','unit','quantity','unit_price','entry_type'], name='unique_order_return_history')
        ]
        verbose_name_plural = "B.4 Order Return History"
